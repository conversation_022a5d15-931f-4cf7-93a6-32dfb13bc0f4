using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record DistributionRowDto
{
    [JsonPropertyName("recycling_area")]
    public string RecyclingArea { get; init; } = null!;

    [JsonPropertyName("reported_tons")]
    public decimal ReportedTons { get; init; }

    [JsonPropertyName("trips")]
    public int Trips { get; init; }

    [JsonPropertyName("calculated_distributed_tons")]
    public decimal CalculatedDistributedTons { get; init; }

    [JsonPropertyName("calculated_total_tons")]
    public decimal CalculatedTotalTons { get; init; }

    [JsonPropertyName("calculated_deviation_tons")]
    public decimal CalculatedDeviationTons { get; init; }

    [JsonPropertyName("toll_shared_route_tons")]
    public decimal TollSharedRouteTons { get; init; }

    [JsonPropertyName("calculated_distribution_toll_percentage")]
    public decimal CalculatedDistributionTollPercentage { get; init; }

    [JsonPropertyName("compensation")]
    public decimal Compensation { get; init; }
}
