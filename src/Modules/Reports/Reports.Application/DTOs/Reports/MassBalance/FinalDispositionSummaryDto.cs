using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record FinalDispositionSummaryDto
{
    [JsonPropertyName("weighing_emvarias_tons")]
    public decimal WeighingEmvariasTons { get; init; }

    [JsonPropertyName("weighing_total_tons")]
    public decimal WeighingTotalTons { get; init; }

    [JsonPropertyName("final_disposition_emvarias_tons")]
    public decimal FinalDispositionEmvariasTons { get; init; }

    [JsonPropertyName("final_disposition_total_tons")]
    public decimal FinalDispositionTotalTons { get; init; }

    [JsonPropertyName("final_disposition_discount_tons")]
    public decimal FinalDispositionDiscountTons { get; init; }
}
