using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record MassBalanceExportDto
{
    [JsonPropertyName("title")]
    public string Title { get; init; } = "Balance de Masas";

    [JsonPropertyName("generation_date")]
    public DateTime GenerationDate { get; init; }

    [JsonPropertyName("period")]
    public string Period { get; init; } = null!;

    [JsonPropertyName("balance_sheet_rows")]
    public List<BalanceSheetRowDto> BalanceSheetRows { get; init; } = null!;

    [JsonPropertyName("final_disposition_summary")]
    public FinalDispositionSummaryDto FinalDispositionSummary { get; init; } = null!;

    [JsonPropertyName("distribution_rows")]
    public List<DistributionRowDto> DistributionRows { get; init; } = null!;
}
