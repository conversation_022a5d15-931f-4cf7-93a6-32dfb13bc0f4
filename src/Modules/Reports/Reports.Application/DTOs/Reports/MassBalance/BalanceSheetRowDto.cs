using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalance;

public record BalanceSheetRowDto
{
    [JsonPropertyName("area_code")]
    public string AreaCode { get; init; } = null!;

    [JsonPropertyName("area_name")]
    public string AreaName { get; init; } = null!;

    [JsonPropertyName("urban_cleaning_tons")]
    public decimal UrbanCleaningTons { get; init; }

    [JsonPropertyName("sweeping_tons")]
    public decimal SweepingTons { get; init; }

    [JsonPropertyName("non_recyclable_tons")]
    public decimal NonRecyclableTons { get; init; }

    [JsonPropertyName("rejection_tons")]
    public decimal RejectionTons { get; init; }

    [JsonPropertyName("recyclable_tons")]
    public decimal RecyclableTons { get; init; }

    [JsonPropertyName("total_by_nuap")]
    public decimal TotalByNUAP { get; init; }

    [JsonPropertyName("discounts")]
    public decimal Discounts { get; init; }
}
