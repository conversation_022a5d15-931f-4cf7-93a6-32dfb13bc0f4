namespace Reports.Application.Common.Constants;

public static class MassBalanceExcelConstants
{
    public const string DefaultExcelDateFormat = "yyyy/MM/dd HH:mm:ss";
    public const string MassBalanceFilePrefix = "Balance_de_Masas_";

    public const int TimestampColumnWidth = 25 * 256;
    public const int TotalByNuapColumnWidth = 18 * 256;

    public const string DecimalFormat = "#,##0.000";
    public const string IntegerFormat = "#,##0";
    public const string CompensationFormat = "#,##0.000;-#,##0.000;0.000";

    public const short TitleFontSize = 16;

    public const int BalanceSheetColumnCount = 12;
    public const int SummaryTableColumnCount = 2;
    public const int DistributionTableColumnCount = 7;

    public static readonly Dictionary<int, string> BalanceSheetColumns = new()
    {
        { 0, "AreaCode" },
        { 1, "AreaName" },
        { 2, "UrbanCleaningTons" },
        { 3, "SweepingTons" },
        { 4, "NonRecyclableTons" },
        { 5, "RejectionTons" },
        { 6, "RecyclableTons" },
        { 7, "TotalF14" },
        { 8, "TotalByNUAP" },
        { 9, "Discounts" },
        { 10, "TotalF34" },
        { 11, "Difference" }
    };

    public static readonly Dictionary<int, string> DistributionColumns = new()
    {
        { 0, "RecyclingArea" },
        { 1, "ReportedTons" },
        { 2, "Trips" },
        { 3, "CalculatedDistributedTons" },
        { 4, "TollSharedRouteTons" },
        { 5, "CalculatedDistributionTollPercentage" },
        { 6, "CalculatedDeviationTons" }
    };

    public static readonly Dictionary<int, string> SummaryColumns = new()
    {
        { 0, "TotalDispositionFinal" },
        { 1, "TotalEmvarias" }
    };

    public static readonly string[] BalanceSheetSubHeaders =
    {
        "",
        "",
        "F14 Toneladas Provenientes del Área de Prestación",
        "",
        "",
        "",
        "",
        "",
        "F34 Disposición Final Operador",
        "",
        "",
        ""
    };

    public static readonly string[] BalanceSheetHeaders =
    {
        "Área de Prestación",
        "Nombre de Área de Prestación",
        "Toneladas de Limpieza Urbana",
        "Toneladas de Barrido",
        "Toneladas de Residuos No Aprovechables",
        "Toneladas de Rechazos de Residuos Aprovechados",
        "Toneladas de Residuos Aprovechables",
        "Toneladas de Barrido + Toneladas de Residuos No Aprovechables",
        "Total por NUAP",
        "Descuentos",
        "Total por NUAP - Descuentos",
        "Diferencia (F34-F14)"
    };

    public static readonly string[] SummaryHeaders =
    {
        "Total Disposición Final",
        "Total Emvarias"
    };

    public static readonly string[] DistributionHeaders =
    {
        "Área de Reciclaje",
        "Toneladas Facturadas",
        "Nro Viajes",
        "Tonxviaje",
        "Toneladas Totales Rutas Compartidas",
        "% Distpeaje",
        "Toneladas a Compensar"
    };

    public static readonly string[] SummaryRowLabels =
    {
        "Integración Balanzas",
        "Disposición Final"
    };

    public const string MainTitle = "Balance de Masas";
    public const string BalanceSheetTitle = "Balance de Masas";
    public const string SummaryTitle = "Disposición Final";
    public const string DistributionTitle = "Distribución";

    public static readonly HashSet<int> BalanceSheetFormulaColumns = new() { 7, 10, 11 };
    public static readonly HashSet<int> DistributionIntegerColumns = new() { 2 };
    public static readonly HashSet<int> DistributionCompensationColumns = new() { 6 };

    public const int HeaderSpacingRows = 1;
    public const int SectionSpacingRows = 2;

    public const string ColumnLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public const string BalanceSheetTotalLabel = "SUMA TOTAL";
    public const string DistributionTotalLabel = "TOTAL";

    public const string TotalKeyword = "TOTAL";
    public const string SumKeyword = "SUMA";
    public const string ConceptKeyword = "Concepto";
    public const string RecyclingAreaKeyword = "Área de Reciclaje";

    public const string DashValue = "-";
    public const double ZeroValue = 0.0;
}
