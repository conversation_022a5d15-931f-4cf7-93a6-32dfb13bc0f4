using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Reports.Application.Common.Constants;
using Reports.Application.DTOs.Reports.MassBalance;

namespace Reports.Application.Common.Helpers;

public static class MassBalanceExcelHelper
{
    public static byte[] GenerateExcel(MassBalanceExportDto exportData)
    {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.CreateSheet(MassBalanceExcelConstants.MainTitle);

        var currentRow = 0;

        currentRow = CreateHeader(sheet, currentRow, exportData.Title, exportData.GenerationDate, exportData.Period);

        currentRow = CreateBalanceSheetTable(sheet, currentRow, exportData.BalanceSheetRows);

        currentRow = CreateSummaryTable(sheet, currentRow, exportData.FinalDispositionSummary);

        currentRow = CreateDistributionTable(sheet, currentRow, exportData.DistributionRows);

        ApplyWorkbookFormatting(sheet);

        SetColumnWidths(sheet);

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    private static int CreateHeader(ISheet sheet, int startRow, string title, DateTime generationDate, string period)
    {
        var currentRow = startRow;

        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue(title);

        var titleMergeRegion = new CellRangeAddress(startRow, startRow, 0, MassBalanceExcelConstants.BalanceSheetColumnCount - 1);
        sheet.AddMergedRegion(titleMergeRegion);
        CreateCellsInRange(titleRow, 1, MassBalanceExcelConstants.BalanceSheetColumnCount - 1);

        var dateRow = sheet.CreateRow(currentRow++);
        var dateCell = dateRow.CreateCell(0);
        dateCell.SetCellValue($"Generado: {generationDate:dd/MM/yyyy HH:mm}");

        var periodRow = sheet.CreateRow(currentRow++);
        var periodCell = periodRow.CreateCell(0);
        periodCell.SetCellValue($"Período: {period}");

        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    private static int CreateBalanceSheetTable(ISheet sheet, int startRow, List<BalanceSheetRowDto> data)
    {
        var currentRow = startRow;

        currentRow = CreateTableHeader(sheet, currentRow, MassBalanceExcelConstants.BalanceSheetTitle, MassBalanceExcelConstants.BalanceSheetColumnCount);

        currentRow = CreateBalanceSheetMultiRowHeaders(sheet, currentRow);

        var dataStartRow = currentRow + 1;
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);
            PopulateBalanceSheetDataRow(dataRow, row, currentRow);
        }

        currentRow = CreateBalanceSheetTotalsRow(sheet, currentRow, dataStartRow, currentRow - 1);

        AddSectionSpacing(sheet, ref currentRow);

        return currentRow;
    }

    private static int CreateSummaryTable(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;

        currentRow = CreateTableHeader(sheet, currentRow, MassBalanceExcelConstants.SummaryTitle, MassBalanceExcelConstants.SummaryTableColumnCount);

        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.SummaryHeaders);

        currentRow = CreateSummaryDataRows(sheet, currentRow, summary);

        AddSectionSpacing(sheet, ref currentRow);

        return currentRow;
    }

    private static int CreateDistributionTable(ISheet sheet, int startRow, List<DistributionRowDto> data)
    {
        var currentRow = startRow;

        currentRow = CreateTableHeader(sheet, currentRow, MassBalanceExcelConstants.DistributionTitle, MassBalanceExcelConstants.DistributionTableColumnCount);

        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.DistributionHeaders);

        var dataStartRow = currentRow + 1;
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);
            PopulateDistributionDataRow(dataRow, row);
        }

        currentRow = CreateDistributionTotalsRow(sheet, currentRow, dataStartRow, currentRow - 1);

        return currentRow;
    }

    #endregion

    #region Helper Methods

    private static int CreateTableHeader(ISheet sheet, int startRow, string title, int columnCount)
    {
        var headerRow = sheet.CreateRow(startRow);
        var headerCell = headerRow.CreateCell(0);
        headerCell.SetCellValue(title);

        var mergeRegion = new CellRangeAddress(startRow, startRow, 0, columnCount - 1);
        sheet.AddMergedRegion(mergeRegion);
        CreateCellsInRange(headerRow, 1, columnCount - 1);

        return startRow + 1;
    }

    private static int CreateColumnHeaders(ISheet sheet, int startRow, string[] headers)
    {
        var headerRow = sheet.CreateRow(startRow);
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
        }
        return startRow + 1;
    }

    private static int CreateBalanceSheetMultiRowHeaders(ISheet sheet, int startRow)
    {
        var currentRow = startRow;

        var firstHeaderRow = sheet.CreateRow(currentRow);
        for (int i = 0; i < MassBalanceExcelConstants.BalanceSheetColumnCount; i++)
        {
            firstHeaderRow.CreateCell(i);
        }

        var areaCell = firstHeaderRow.GetCell(0);
        areaCell.SetCellValue("Área de Prestación");
        var areaMergeRegion = new CellRangeAddress(currentRow, currentRow + 1, 0, 0);
        sheet.AddMergedRegion(areaMergeRegion);

        var nameCell = firstHeaderRow.GetCell(1);
        nameCell.SetCellValue("Nombre de Área de Prestación");
        var nameMergeRegion = new CellRangeAddress(currentRow, currentRow + 1, 1, 1);
        sheet.AddMergedRegion(nameMergeRegion);

        var f14Cell = firstHeaderRow.GetCell(2);
        f14Cell.SetCellValue("F14 Toneladas Provenientes del Área de Prestación");
        var f14MergeRegion = new CellRangeAddress(currentRow, currentRow, 2, 7);
        sheet.AddMergedRegion(f14MergeRegion);

        var f34Cell = firstHeaderRow.GetCell(8);
        f34Cell.SetCellValue("F34 Disposición Final Operador");
        var f34MergeRegion = new CellRangeAddress(currentRow, currentRow, 8, 11);
        sheet.AddMergedRegion(f34MergeRegion);

        currentRow++;

        var secondHeaderRow = sheet.CreateRow(currentRow);
        for (int i = 0; i < MassBalanceExcelConstants.BalanceSheetColumnCount; i++)
        {
            secondHeaderRow.CreateCell(i);
        }

        secondHeaderRow.GetCell(2).SetCellValue("Toneladas de Limpieza Urbana");
        secondHeaderRow.GetCell(3).SetCellValue("Toneladas de Barrido");
        secondHeaderRow.GetCell(4).SetCellValue("Toneladas de Residuos No Aprovechables");
        secondHeaderRow.GetCell(5).SetCellValue("Toneladas de Rechazos de Residuos Aprovechados");
        secondHeaderRow.GetCell(6).SetCellValue("Toneladas de Residuos Aprovechables");
        secondHeaderRow.GetCell(7).SetCellValue("Toneladas de Barrido + Toneladas de Residuos No Aprovechables");

        secondHeaderRow.GetCell(8).SetCellValue("Total por NUAP");
        secondHeaderRow.GetCell(9).SetCellValue("Descuentos");
        secondHeaderRow.GetCell(10).SetCellValue("Total por NUAP - Descuentos");
        secondHeaderRow.GetCell(11).SetCellValue("Diferencia (F34-F14)");

        return currentRow + 1;
    }

    private static void PopulateBalanceSheetDataRow(IRow dataRow, BalanceSheetRowDto row, int rowNumber)
    {
        dataRow.CreateCell(0).SetCellValue(row.AreaCode);
        dataRow.CreateCell(1).SetCellValue(row.AreaName);
        dataRow.CreateCell(2).SetCellValue((double)row.UrbanCleaningTons);
        dataRow.CreateCell(3).SetCellValue((double)row.SweepingTons);
        dataRow.CreateCell(4).SetCellValue((double)row.NonRecyclableTons);
        dataRow.CreateCell(5).SetCellValue((double)row.RejectionTons);
        dataRow.CreateCell(6).SetCellValue((double)row.RecyclableTons);

        var totalF14Cell = dataRow.CreateCell(7);
        totalF14Cell.SetCellFormula($"D{rowNumber}+E{rowNumber}");

        dataRow.CreateCell(8).SetCellValue((double)row.TotalByNUAP);
        dataRow.CreateCell(9).SetCellValue((double)row.Discounts);

        var totalF34Cell = dataRow.CreateCell(10);
        totalF34Cell.SetCellFormula($"I{rowNumber}-J{rowNumber}");

        var differenceCell = dataRow.CreateCell(11);
        if (row.AreaName == "Otros")
        {
            differenceCell.SetCellValue(MassBalanceExcelConstants.ZeroValue);
        }
        else
        {
            differenceCell.SetCellFormula($"K{rowNumber}-H{rowNumber}+J{rowNumber}");
        }
    }

    private static void PopulateDistributionDataRow(IRow dataRow, DistributionRowDto row)
    {
        dataRow.CreateCell(0).SetCellValue(row.RecyclingArea);
        dataRow.CreateCell(1).SetCellValue((double)row.ReportedTons);
        dataRow.CreateCell(2).SetCellValue(row.Trips);
        dataRow.CreateCell(3).SetCellValue((double)row.CalculatedDistributedTons);
        dataRow.CreateCell(4).SetCellValue((double)row.TollSharedRouteTons);
        dataRow.CreateCell(5).SetCellValue((double)row.CalculatedDistributionTollPercentage);
        dataRow.CreateCell(6).SetCellValue((double)row.CalculatedDeviationTons);
    }

    private static int CreateSummaryDataRows(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;

        var dataRow = sheet.CreateRow(currentRow++);
        dataRow.CreateCell(0).SetCellValue((double)summary.FinalDispositionTotalTons);
        dataRow.CreateCell(1).SetCellValue((double)summary.FinalDispositionEmvariasTons);

        return currentRow;
    }

    private static void CreateCellsInRange(IRow row, int startCol, int endCol)
    {
        for (int col = startCol; col <= endCol; col++)
        {
            row.CreateCell(col);
        }
    }

    private static void AddSectionSpacing(ISheet sheet, ref int currentRow)
    {
        for (int i = 0; i < MassBalanceExcelConstants.SectionSpacingRows; i++)
        {
            sheet.CreateRow(currentRow++);
        }
    }

    private static string GetColumnLetter(int columnIndex)
    {
        if (columnIndex < 26)
            return MassBalanceExcelConstants.ColumnLetters[columnIndex].ToString();

        return MassBalanceExcelConstants.ColumnLetters[columnIndex / 26 - 1].ToString() +
               MassBalanceExcelConstants.ColumnLetters[columnIndex % 26].ToString();
    }

    private static int CreateBalanceSheetTotalsRow(ISheet sheet, int currentRow, int dataStartRow, int dataEndRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(1).SetCellValue(MassBalanceExcelConstants.BalanceSheetTotalLabel);

        for (int col = 2; col <= MassBalanceExcelConstants.BalanceSheetColumnCount - 1; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
        }

        return currentRow;
    }

    private static int CreateDistributionTotalsRow(ISheet sheet, int currentRow, int dataStartRow, int dataEndRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(0).SetCellValue(MassBalanceExcelConstants.DistributionTotalLabel);

        for (int col = 1; col <= MassBalanceExcelConstants.DistributionTableColumnCount - 1; col++)
        {
            var totalCell = totalsRow.CreateCell(col);

            if (col == 3)
            {
                totalCell.SetCellFormula($"SUMPRODUCT({GetColumnLetter(2)}{dataStartRow}:{GetColumnLetter(2)}{dataEndRow},{GetColumnLetter(3)}{dataStartRow}:{GetColumnLetter(3)}{dataEndRow})");
            }
            else if (col == 4)
            {
                totalCell.SetCellValue(MassBalanceExcelConstants.DashValue);
            }
            else
            {
                totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
            }
        }

        return currentRow;
    }

    private static void SetColumnWidths(ISheet sheet)
    {
        for (int i = 0; i < 15; i++)
        {
            sheet.AutoSizeColumn(i);
        }

        sheet.SetColumnWidth(0, MassBalanceExcelConstants.TimestampColumnWidth);
        sheet.SetColumnWidth(8, MassBalanceExcelConstants.TotalByNuapColumnWidth);
    }

    #endregion

    #region Formatting Methods

    private static void ApplyWorkbookFormatting(ISheet sheet)
    {
        var workbook = sheet.Workbook;
        var styles = CreateAllStyles(workbook);

        ApplyTitleFormatting(sheet, styles.TitleStyle);
        ApplyHeaderFormatting(sheet, styles.HeaderStyle);
        ApplyDataFormatting(sheet, styles);
        ApplyTotalFormatting(sheet, styles.TotalStyle);
        ApplyDistributionTableBorders(sheet, workbook);
        ApplyBordersToMergedRegions(sheet);
    }

    private static ExcelStyles CreateAllStyles(IWorkbook workbook)
    {
        return new ExcelStyles
        {
            TitleStyle = CreateTitleStyle(workbook),
            HeaderStyle = CreateHeaderStyle(workbook),
            DataStyle = CreateDataStyle(workbook),
            IntegerStyle = CreateIntegerStyle(workbook),
            CalculatedStyle = CreateCalculatedStyle(workbook),
            CompensationStyle = CreateCompensationStyle(workbook),
            TotalStyle = CreateTotalStyle(workbook)
        };
    }

    private static ICellStyle CreateTitleStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.FontHeightInPoints = MassBalanceExcelConstants.TitleFontSize;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        return style;
    }

    private static ICellStyle CreateHeaderStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static ICellStyle CreateDataStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    private static ICellStyle CreateIntegerStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.IntegerFormat);
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    private static ICellStyle CreateCalculatedStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static ICellStyle CreateCompensationStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static ICellStyle CreateTotalStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);
        style.FillForegroundColor = IndexedColors.Grey25Percent.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static void ApplyTitleFormatting(ISheet sheet, ICellStyle titleStyle)
    {
        for (int row = 0; row < 3; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = 0; col < MassBalanceExcelConstants.BalanceSheetColumnCount; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        cell.CellStyle = titleStyle;
                    }
                }
            }
        }
    }

    private static void ApplyHeaderFormatting(ISheet sheet, ICellStyle headerStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                if (IsSubHeaderRow(row))
                {
                    ApplyStyleToRowRange(sheet, rowIndex, headerStyle, 0, MassBalanceExcelConstants.BalanceSheetColumnCount - 1);
                    continue;
                }

                var firstCell = row.GetCell(0);
                if (firstCell != null && firstCell.CellType == CellType.String)
                {
                    var cellValue = firstCell.StringCellValue;
                    var columnCount = GetColumnCountForHeader(cellValue);

                    if (columnCount > 0)
                    {
                        ApplyStyleToRowRange(sheet, rowIndex, headerStyle, 0, columnCount - 1);
                    }
                }
            }
        }
    }

    private static void ApplyDataFormatting(ISheet sheet, ExcelStyles styles)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsDataRow(row))
            {
                ApplyDataRowFormatting(row, styles);
            }
        }
    }

    private static void ApplyTotalFormatting(ISheet sheet, ICellStyle totalStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsTotalRow(row))
            {
                var columnCount = GetColumnCountForTotalRow(sheet, rowIndex);
                ApplyStyleToRowRange(sheet, rowIndex, totalStyle, 0, columnCount - 1);
            }
        }
    }

    private static void ApplyDistributionTableBorders(ISheet sheet, IWorkbook workbook)
    {
        var (startRow, endRow) = FindDistributionTableBounds(sheet);
        if (startRow == -1 || endRow == -1) return;

        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                ApplyDistributionRowBorders(row, workbook, rowIndex, startRow, endRow);
            }
        }
    }

    private static void ApplyBordersToMergedRegions(ISheet sheet)
    {
        var workbook = sheet.Workbook;

        for (int i = 0; i < sheet.NumMergedRegions; i++)
        {
            var mergedRegion = sheet.GetMergedRegion(i);
            ApplyBordersToMergedRegion(sheet, workbook, mergedRegion);
        }
    }

    private static void ApplyStyleToRowRange(ISheet sheet, int rowIndex, ICellStyle style, int startCol, int endCol)
    {
        var row = sheet.GetRow(rowIndex);
        if (row != null)
        {
            for (int col = startCol; col <= endCol; col++)
            {
                var cell = row.GetCell(col);
                if (cell != null)
                {
                    cell.CellStyle = style;
                }
            }
        }
    }

    private static int GetColumnCountForHeader(string cellValue)
    {
        return cellValue switch
        {
            var value when value.Contains(MassBalanceExcelConstants.BalanceSheetTitle) => MassBalanceExcelConstants.BalanceSheetColumnCount,
            MassBalanceExcelConstants.SummaryTitle => MassBalanceExcelConstants.SummaryTableColumnCount,
            MassBalanceExcelConstants.ConceptKeyword => MassBalanceExcelConstants.SummaryTableColumnCount,
            MassBalanceExcelConstants.DistributionTitle => MassBalanceExcelConstants.DistributionTableColumnCount,
            MassBalanceExcelConstants.RecyclingAreaKeyword => MassBalanceExcelConstants.DistributionTableColumnCount,
            var value when value == MassBalanceExcelConstants.BalanceSheetHeaders[0] => MassBalanceExcelConstants.BalanceSheetColumnCount,
            var value when value == MassBalanceExcelConstants.SummaryHeaders[0] => MassBalanceExcelConstants.SummaryTableColumnCount,
            var value when value == MassBalanceExcelConstants.DistributionHeaders[0] => MassBalanceExcelConstants.DistributionTableColumnCount,
            _ => 0
        };
    }

    private static bool IsSubHeaderRow(IRow row)
    {
        var f14Cell = row.GetCell(2);
        if (f14Cell != null && f14Cell.CellType == CellType.String && f14Cell.StringCellValue.Contains("F14 Toneladas Provenientes del Área de Prestación"))
        {
            return true;
        }

        var f34Cell = row.GetCell(8);
        if (f34Cell != null && f34Cell.CellType == CellType.String && f34Cell.StringCellValue.Contains("F34 Disposición Final Operador"))
        {
            return true;
        }

        var detailCell = row.GetCell(2);
        if (detailCell != null && detailCell.CellType == CellType.String && detailCell.StringCellValue == "Toneladas de Limpieza Urbana")
        {
            return true;
        }

        var areaCell = row.GetCell(0);
        if (areaCell != null && areaCell.CellType == CellType.String && areaCell.StringCellValue == "Área de Prestación")
        {
            return true;
        }

        return false;
    }

    private static bool IsDataRow(IRow row)
    {
        var firstCell = row.GetCell(0) ?? row.GetCell(1);
        var cellValue = "";

        if (firstCell != null && firstCell.CellType == CellType.String)
        {
            cellValue = firstCell.StringCellValue;
        }

        if (cellValue.Contains(MassBalanceExcelConstants.TotalKeyword) || cellValue.Contains(MassBalanceExcelConstants.SumKeyword))
            return false;

        if (cellValue.Contains(MassBalanceExcelConstants.SummaryTitle) || cellValue.Contains(MassBalanceExcelConstants.DistributionTitle) ||
            cellValue.Contains(MassBalanceExcelConstants.BalanceSheetTitle))
            return false;

        if (cellValue == MassBalanceExcelConstants.ConceptKeyword || cellValue == MassBalanceExcelConstants.RecyclingAreaKeyword ||
            cellValue == MassBalanceExcelConstants.BalanceSheetHeaders[0] || cellValue == MassBalanceExcelConstants.SummaryHeaders[0] ||
            cellValue == MassBalanceExcelConstants.DistributionHeaders[0])
            return false;

        if (string.IsNullOrWhiteSpace(cellValue))
            return false;

        return true;
    }

    private static bool IsTotalRow(IRow row)
    {
        var firstCell = row.GetCell(0) ?? row.GetCell(1);
        return firstCell != null && firstCell.CellType == CellType.String &&
               (firstCell.StringCellValue.Contains(MassBalanceExcelConstants.TotalKeyword) || firstCell.StringCellValue.Contains(MassBalanceExcelConstants.SumKeyword));
    }

    private static void ApplyDataRowFormatting(IRow row, ExcelStyles styles)
    {
        for (int col = 0; col < MassBalanceExcelConstants.BalanceSheetColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                if (MassBalanceExcelConstants.BalanceSheetFormulaColumns.Contains(col))
                {
                    cell.CellStyle = styles.CalculatedStyle;
                }
                else if (col >= 2 || (col <= 1 && cell.CellType != CellType.Blank))
                {
                    cell.CellStyle = styles.DataStyle;
                }
            }
        }

        for (int col = 0; col < MassBalanceExcelConstants.DistributionTableColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
                {
                    cell.CellStyle = styles.CompensationStyle;
                }
                else if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
                {
                    cell.CellStyle = styles.IntegerStyle;
                }
                else
                {
                    cell.CellStyle = styles.DataStyle;
                }
            }
        }
    }

    private static int GetColumnCountForTotalRow(ISheet sheet, int rowIndex)
    {
        for (int checkRow = rowIndex - 1; checkRow >= 0; checkRow--)
        {
            var checkRowObj = sheet.GetRow(checkRow);
            if (checkRowObj != null)
            {
                var checkCell = checkRowObj.GetCell(0);
                if (checkCell != null && checkCell.CellType == CellType.String)
                {
                    var cellValue = checkCell.StringCellValue;
                    if (cellValue == MassBalanceExcelConstants.DistributionTitle)
                    {
                        return MassBalanceExcelConstants.DistributionTableColumnCount;
                    }
                    else if (cellValue.Contains(MassBalanceExcelConstants.BalanceSheetTitle))
                    {
                        return MassBalanceExcelConstants.BalanceSheetColumnCount;
                    }
                }
            }
        }
        return MassBalanceExcelConstants.BalanceSheetColumnCount;
    }

    private static (int startRow, int endRow) FindDistributionTableBounds(ISheet sheet)
    {
        int startRow = -1, endRow = -1;

        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0);
                if (firstCell != null && firstCell.CellType == CellType.String)
                {
                    var cellValue = firstCell.StringCellValue;
                    if (cellValue == MassBalanceExcelConstants.DistributionTitle)
                    {
                        startRow = rowIndex;
                    }
                    else if (startRow != -1 && cellValue == MassBalanceExcelConstants.DistributionTotalLabel)
                    {
                        endRow = rowIndex;
                        break;
                    }
                }
            }
        }

        return (startRow, endRow);
    }

    private static void ApplyDistributionRowBorders(IRow row, IWorkbook workbook, int rowIndex, int startRow, int endRow)
    {
        bool isTopRow = rowIndex == startRow;
        bool isBottomRow = rowIndex == endRow;
        bool isHeaderRow = IsDistributionHeaderRow(row);

        for (int col = 0; col < MassBalanceExcelConstants.DistributionTableColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                bool isLeftColumn = col == 0;
                bool isRightColumn = col == MassBalanceExcelConstants.DistributionTableColumnCount - 1;

                var borderStyle = CreateDistributionBorderStyle(workbook, col, isHeaderRow, isBottomRow, isTopRow, isLeftColumn, isRightColumn);
                cell.CellStyle = borderStyle;
            }
        }
    }

    private static bool IsDistributionHeaderRow(IRow row)
    {
        var firstCell = row.GetCell(0);
        if (firstCell != null && firstCell.CellType == CellType.String)
        {
            var cellValue = firstCell.StringCellValue;
            return cellValue == MassBalanceExcelConstants.DistributionTitle || cellValue == MassBalanceExcelConstants.RecyclingAreaKeyword;
        }
        return false;
    }

    private static ICellStyle CreateDistributionBorderStyle(IWorkbook workbook, int col, bool isHeaderRow, bool isBottomRow, bool isTopRow, bool isLeftColumn, bool isRightColumn)
    {
        ICellStyle borderStyle;

        if (isHeaderRow)
        {
            borderStyle = workbook.CreateCellStyle();
            borderStyle.CloneStyleFrom(CreateHeaderStyle(workbook));
            borderStyle.BorderTop = BorderStyle.Thick;
            borderStyle.BorderBottom = BorderStyle.Thick;
            borderStyle.BorderLeft = BorderStyle.Thick;
            borderStyle.BorderRight = BorderStyle.Thick;
        }
        else if (isBottomRow)
        {
            if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, false, false, true, false, false);
            }
            else if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, false, false, true, false, false);
            }
            else
            {
                borderStyle = CreateDistributionTableBorderStyle(workbook, false, false, true, false, false);
            }

            borderStyle.BorderTop = BorderStyle.Thick;
            borderStyle.BorderBottom = BorderStyle.Thick;
            borderStyle.BorderLeft = BorderStyle.Thick;
            borderStyle.BorderRight = BorderStyle.Thick;
        }
        else
        {
            if (MassBalanceExcelConstants.DistributionIntegerColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
            else if (MassBalanceExcelConstants.DistributionCompensationColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
            else
            {
                borderStyle = CreateDistributionTableBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
        }

        return borderStyle;
    }

    private static ICellStyle CreateDistributionTableBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.DecimalFormat);

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private static ICellStyle CreateDistributionTableIntegerBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.IntegerFormat);

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private static ICellStyle CreateDistributionTableCompensationBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private static void ApplyBordersToMergedRegion(ISheet sheet, IWorkbook workbook, CellRangeAddress mergedRegion)
    {
        for (int row = mergedRegion.FirstRow; row <= mergedRegion.LastRow; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = mergedRegion.FirstColumn; col <= mergedRegion.LastColumn; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        var currentStyle = cell.CellStyle;
                        var newStyle = workbook.CreateCellStyle();
                        newStyle.CloneStyleFrom(currentStyle);

                        bool isTopRow = row == mergedRegion.FirstRow;
                        bool isBottomRow = row == mergedRegion.LastRow;
                        bool isLeftColumn = col == mergedRegion.FirstColumn;
                        bool isRightColumn = col == mergedRegion.LastColumn;

                        if (isTopRow && currentStyle.BorderTop != BorderStyle.None)
                        {
                            newStyle.BorderTop = currentStyle.BorderTop;
                            newStyle.TopBorderColor = currentStyle.TopBorderColor;
                        }
                        if (isBottomRow && currentStyle.BorderBottom != BorderStyle.None)
                        {
                            newStyle.BorderBottom = currentStyle.BorderBottom;
                            newStyle.BottomBorderColor = currentStyle.BottomBorderColor;
                        }
                        if (isLeftColumn && currentStyle.BorderLeft != BorderStyle.None)
                        {
                            newStyle.BorderLeft = currentStyle.BorderLeft;
                            newStyle.LeftBorderColor = currentStyle.LeftBorderColor;
                        }
                        if (isRightColumn && currentStyle.BorderRight != BorderStyle.None)
                        {
                            newStyle.BorderRight = currentStyle.BorderRight;
                            newStyle.RightBorderColor = currentStyle.RightBorderColor;
                        }

                        cell.CellStyle = newStyle;
                    }
                }
            }
        }
    }

    #endregion

    #region Helper Classes

    private class ExcelStyles
    {
        public ICellStyle TitleStyle { get; set; } = null!;
        public ICellStyle HeaderStyle { get; set; } = null!;
        public ICellStyle DataStyle { get; set; } = null!;
        public ICellStyle IntegerStyle { get; set; } = null!;
        public ICellStyle CalculatedStyle { get; set; } = null!;
        public ICellStyle CompensationStyle { get; set; } = null!;
        public ICellStyle TotalStyle { get; set; } = null!;
    }

    #endregion
}
