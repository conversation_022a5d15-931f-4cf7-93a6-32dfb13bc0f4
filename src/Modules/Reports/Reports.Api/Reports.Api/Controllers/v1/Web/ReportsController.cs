using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.DTOs.Reports;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.Features.Web.Queries.Reports.ExportCompressedReports;
using Reports.Application.Features.Web.Queries.Reports.ExportDiscounts;
using Reports.Application.Features.Web.Queries.Reports.ExportMassBalance;
using Reports.Application.Features.Web.Queries.Reports.ExportReport;
using Reports.Application.Features.Web.Queries.Reports.GetReportPreview;
using Reports.Application.Features.Web.Queries.Reports.MassBalance;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
//[Authorize]
public class ReportsController : OrionController
{
    /// <summary>
    /// Obtiene la vista previa de un reporte.
    /// </summary>
    /// <param name="request">Filtros del reporte a generar.</param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<F14PreviewResponseDto>))]
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<F34PreviewResponseDto>))]
    [HttpGet("Preview")]
    public async Task<IActionResult> GetReportPreview([FromQuery] GetReportPreviewRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.ReportPreview);
    }

    /// <summary>
    /// Obtiene el objeto representativo de un reporte de balance de masas
    /// de un periodo determinado
    /// </summary>
    /// <param name="request">Filtros del reporte a generar.</param>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(MassBalanceResponseDto))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("MassBalance")]
    public async Task<IActionResult> GetMassBalance([FromQuery] GetMassBalanceRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.MassBalance);
    }

    /// <summary>
    /// Exporta el reporte de balance de masas en formato Excel
    /// con layout exacto del PDF incluyendo tres tablas posicionadas una debajo de otra
    /// </summary>
    /// <param name="request">Filtros del reporte a exportar.</param>
    /// <remarks>
    /// El archivo Excel generado replica exactamente el layout del PDF con:
    /// - Sección de encabezado con título, fecha de generación e información del período
    /// - Tabla principal de Balance de Masas combinando datos F14 (Recolección y Transporte) y F34 (Disposición Final)
    /// - Tabla resumen de Disposición Final en la parte inferior de la página 1
    /// - Tabla de Distribución en la página 2 con porcentajes calculados y compensación
    ///
    /// Formatos soportados: xlsx, xls, csv (especificado en header X-ExcelFormat o parámetro ExcelFormat)
    /// </remarks>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.Conflict, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.NoContent, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("MassBalance/Export")]
    public async Task ExportMassBalance([FromQuery] ExportMassBalanceRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }

    /// <summary>
    /// Exporta los reportes 14 y 34
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpGet("Export")]
    public async Task ExportReport([FromQuery] ReportExportRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }

    /// <summary>
    /// Genera un ZIP con los reportes formateados según la regulación de la intendencia
    /// de un periodo seleccionado en formato CSV
    /// </summary>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpGet("ExportCompressedReports")]
    public async Task ExportCompressedReports([FromQuery] CompressedReportsRequestDto requestDto)
    {
        var request = new ExportCompressedReportsRequest(requestDto.FromDate,
            requestDto.ToDate,
            requestDto.SortFields,
            requestDto.SortDirection
            );

        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
    
    /// <summary>
    /// Exporta un excel o csv con un listado de descuentos de toneladas
    /// por numero de tiquete para un periodo determinado
    /// </summary>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(FileResult))]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError, Type = typeof(Error))]
    [HttpGet("DiscountsReport")]
    public async Task ExportDiscounts([FromQuery] ExportDiscountsRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
}