using System.Globalization;
using System.Reflection;
using NPOI.SS.UserModel;
using NPOI.SS.Util;

namespace Shared.Application.Common.Helpers;

public static class ExcelHelper
{
    private const string DefaultCultureRegion = "es-419";

    public static void CreateTitleRow(this ISheet sheet, string tableTitleRow, PropertyInfo[] properties)
    {
        var titleRow = sheet.CreateRow(0);

        var headerFont = sheet.Workbook.CreateFont();
        headerFont.IsBold = true;

        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue(tableTitleRow);

        var cellStyle = sheet.Workbook.CreateCellStyle();
        cellStyle.SetFont(headerFont);
        cellStyle.Alignment = HorizontalAlignment.Center;
        cellStyle.WrapText = true;
        titleCell.CellStyle = cellStyle;

        var lineCount = tableTitleRow.Split(new char[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
        titleRow.HeightInPoints = lineCount * sheet.DefaultRowHeightInPoints;

        var mergeRegion = new CellRangeAddress(0, 0, 0, properties.Length - 1);
        sheet.AddMergedRegion(mergeRegion);
    }

    public static PropertyInfo[] CreateColumnNamesRow(this ISheet sheet, PropertyInfo[] properties, bool hasTitleRow)
    {
        var headerRow = sheet.CreateRow(hasTitleRow ? 1 : 0);

        for (var columnIndex = 0; columnIndex < properties.Length; columnIndex++)
        {
            var headerName = properties[columnIndex]
                                .Name;

            headerRow.CreateCell(column: columnIndex)
                     .SetCellValue(value: headerName);
        }

        return properties;
    }

    public static void CreateDataRows<TEntity>(this ISheet sheet, List<TEntity> entities, PropertyInfo[] properties, bool hasTitleRow, string dateFormat)
    {
        for (var rowNumber = 0; rowNumber < entities.Count; rowNumber++)
        {
            var dataRow = sheet.CreateRow(rowNumber + (hasTitleRow ? 2 : 1));

            for (var columnIndex = 0; columnIndex < properties.Length; columnIndex++)
            {
                var value = properties[columnIndex]
                    .GetValue(entities[index: rowNumber])!;

                CreateTypedCell(value, dataRow, columnIndex, dateFormat);
            }
        }
    }

    private static void CreateTypedCell(object? value, IRow dataRow, int columnIndex, string dateFormat)
    {
        var culture = CultureInfo.CreateSpecificCulture(DefaultCultureRegion);

        switch (value)
        {
            case DateTime dateTime:
                dataRow.CreateCell(columnIndex)
                    .SetCellValue(dateTime.ToString(dateFormat));
                break;
            case DateTimeOffset dateTimeOffset:
                dataRow.CreateCell(columnIndex)
                    .SetCellValue(dateTimeOffset.ToString(dateFormat));
                break;
            case string strValue:
                dataRow.CreateCell(columnIndex)
                    .SetCellValue(strValue);
                break;
            case int intValue:
                dataRow.CreateCell(columnIndex, CellType.Numeric)
                    .SetCellValue(Convert.ToDouble(intValue));
                break;
            case long longValue:
                dataRow.CreateCell(columnIndex, CellType.Numeric)
                    .SetCellValue(Convert.ToDouble(longValue));
                break;
            case decimal decimalValue:
                dataRow.CreateCell(columnIndex, CellType.Numeric)
                    .SetCellValue(Convert.ToDouble(decimalValue));
                break;
            case bool boolValue:
                dataRow.CreateCell(columnIndex, CellType.Boolean)
                    .SetCellValue(boolValue.ToString(culture));
                break;
            default:
                dataRow.CreateCell(columnIndex, CellType.String)
                    .SetCellValue(value?.ToString());
                break;
        }
    }

    public static PropertyInfo[] CreateHeadersForCsv<TEntity>(this StreamWriter writer, PropertyInfo[] allProperties) where TEntity : class
    {
        writer.WriteLine(string.Join(",", allProperties.Select(prop => prop.Name)));

        return allProperties;
    }

    public static void CreateRowsForCsv<TEntity>(this StreamWriter writer, IEnumerable<TEntity> entities, PropertyInfo[] filteredProperties, string dateFormat) where TEntity : class
    {
        foreach (TEntity row in entities)
        {
            writer.WriteLine(string.Join(",", filteredProperties.Select(prop =>
            {
                var value = prop.GetValue(row);

                return value switch
                {
                    DateTime dateValue => dateValue.ToString(dateFormat),

                    DateTimeOffset dateTimeOffsetValue => dateTimeOffsetValue.ToString(dateFormat),

                    string strValue => "\"" + strValue.Replace("\"", "\"\"") + "\"",

                    decimal decimalValue => FormatDecimalForCsv(decimalValue),

                    double doubleValue => FormatDecimalForCsv((decimal)doubleValue),

                    float floatValue => FormatDecimalForCsv((decimal)floatValue),

                    _ => value?.ToString(),
                };
            })));
        }
    }

    private static string FormatDecimalForCsv(decimal value)
    {
        return value.ToString("0.###", CultureInfo.InvariantCulture);
    }

    public static int AddEmptyRows(this ISheet sheet, int startRow, int numberOfRows)
    {
        for (int i = 0; i < numberOfRows; i++)
        {
            sheet.CreateRow(startRow + i);
        }
        return startRow + numberOfRows;
    }

    public static void MergeCells(this ISheet sheet, int firstRow, int lastRow, int firstCol, int lastCol)
    {
        var mergeRegion = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        sheet.AddMergedRegion(mergeRegion);
    }

    public static void AutoSizeColumns(this ISheet sheet, int startColumn, int endColumn, int minWidth = 12)
    {
        for (int col = startColumn; col <= endColumn; col++)
        {
            sheet.AutoSizeColumn(col);

            var currentWidth = sheet.GetColumnWidth(col);
            var minWidthInUnits = minWidth * 256;

            if (currentWidth < minWidthInUnits)
            {
                sheet.SetColumnWidth(col, minWidthInUnits);
            }
        }
    }

    public static void SetRowHeight(this ISheet sheet, int rowIndex, float heightInPoints)
    {
        var row = sheet.GetRow(rowIndex) ?? sheet.CreateRow(rowIndex);
        row.HeightInPoints = heightInPoints;
    }
}